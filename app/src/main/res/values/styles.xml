<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
    </style>


    <style name="Widget.JobRec.Button" parent="Widget.DUTCareerHub.Button">

    </style>

    <style name="Widget.JobRec.Button.Outlined" parent="Widget.DUTCareerHub.Button.Outlined">

    </style>


    <style name="Widget.DUTCareerHub.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style>

    <style name="Widget.DUTCareerHub.TextInputEditText" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorSecondary</item>
    </style>

    <!-- CareerWorx styles for compatibility -->
    <style name="Widget.CareerWorx.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style>

    <style name="Widget.CareerWorx.TextInputEditText" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorSecondary</item>
    </style>

    <!-- CareerWorx Button styles for compatibility -->
    <style name="Widget.CareerWorx.Button" parent="Widget.DUTCareerHub.Button">
    </style>

    <style name="Widget.CareerWorx.Button.Outlined" parent="Widget.DUTCareerHub.Button.Outlined">
    </style>

    <style name="Widget.CareerWorx.Button.Secondary" parent="Widget.DUTCareerHub.Button.Secondary">
    </style>

    <style name="Widget.CareerWorx.Card" parent="Widget.DUTCareerHub.Card">
    </style>

    <!-- CareerWorx Toolbar styles for compatibility -->
    <style name="Widget.CareerWorx.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="navigationIconTint">@color/white</item>
    </style>

    <style name="Widget.CareerWorx.Toolbar.Company" parent="Widget.CareerWorx.Toolbar">
    </style>
</resources>