<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.DUTCareerHub.Toolbar"
            app:title="Edit Company Profile" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Company Name">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/companyNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Industry">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/industryInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Registration Number">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/registrationNumberInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Company Size">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/companySizeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Location">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/locationInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Website">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/websiteInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Description">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/descriptionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3"/>
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Contact Person Details"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Contact Person Name">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/contactPersonNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Contact Person Email">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/contactPersonEmailInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Contact Person Phone">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/contactPersonPhoneInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone"/>
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/saveButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Changes"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/primary"/>

        </LinearLayout>
    </ScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>