<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".CompanyCalendarActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Calendar View -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <CalendarView
                        android:id="@+id/calendarView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="8dp" />

                </com.google.android.material.card.MaterialCardView>

                <!-- Upcoming Interviews Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Upcoming Interviews &amp; Events"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <!-- No Upcoming Interviews Message -->
                    <TextView
                        android:id="@+id/noUpcomingInterviewsText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="16dp"
                        android:gravity="center"
                        android:text="No upcoming interviews or events"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <!-- Upcoming Interviews RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/upcomingInterviewsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:maxHeight="300dp"
                        tools:listitem="@layout/item_upcoming_event" />

                </LinearLayout>

                <!-- Tomorrow's Interviews Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tomorrowInterviewsHeader"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Tomorrow's Schedule"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <!-- No Tomorrow Interviews Message -->
                    <TextView
                        android:id="@+id/noTomorrowInterviewsText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="16dp"
                        android:gravity="center"
                        android:text="No interviews or events scheduled for tomorrow"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <!-- Tomorrow Interviews RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/tomorrowInterviewsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:maxHeight="200dp"
                        tools:listitem="@layout/item_calendar_event" />

                </LinearLayout>

                <!-- Events for Selected Date Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Events for Selected Date"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <!-- No Events Message -->
                    <TextView
                        android:id="@+id/noEventsText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="32dp"
                        android:gravity="center"
                        android:text="No events scheduled for this date"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp"
                        android:visibility="gone" />

                    <!-- Events RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/eventsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_calendar_event" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Add Event FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addEventFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Add Company Event"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
