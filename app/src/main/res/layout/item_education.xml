<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="#E0E0E0">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etEducationInstitution"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Institution"
                android:textColor="?android:attr/textColorPrimary"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etEducationDegree"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Degree"
                android:textColor="?android:attr/textColorPrimary"/>
        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etEducationStartDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Start Date"
                    android:textColor="?android:attr/textColorPrimary"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etEducationEndDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="End Date"
                    android:textColor="?android:attr/textColorPrimary"/>
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etEducationDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Description"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:gravity="top"
                android:textColor="?android:attr/textColorPrimary"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRemoveEducation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Remove"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:textColor="?attr/colorError"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>