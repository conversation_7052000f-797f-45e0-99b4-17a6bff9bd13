<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.DUTCareerHub.Toolbar"
            app:title="Dashboard"
            app:titleTextAppearance="@style/TextAppearance.AppCompat.Medium" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">


                <TextView
                    android:id="@+id/welcomeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Welcome back!"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="?android:textColorPrimary"
                    android:layout_marginBottom="24dp"/>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp"
                        app:strokeWidth="1dp"
                        app:strokeColor="#E0E0E0">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Active Jobs"
                                android:textColor="?android:textColorPrimary"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/activeJobsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="?android:textColorPrimary"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp"
                        app:strokeWidth="1dp"
                        app:strokeColor="#E0E0E0">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Applications"
                                android:textColor="?android:textColorPrimary"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/applicationsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="?android:textColorPrimary"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Recent Activity"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:textColorPrimary"
                    android:layout_marginBottom="16dp"/>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E0E0E0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/recentActivityTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="No recent activity"
                            android:textColor="?android:textColorPrimary"
                            android:textSize="16sp"
                            android:textStyle="bold"/>

                        <TextView
                            android:id="@+id/recentActivityDescription"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Your recent activities will appear here"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Quick Actions"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:textColorPrimary"
                    android:layout_marginBottom="16dp"/>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnPostJob"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Post Job"
                        android:textColor="@android:color/white"
                        app:backgroundTint="@android:color/black"
                        app:cornerRadius="8dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnViewApplications"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="View Applications"
                        android:textColor="@android:color/white"
                        app:backgroundTint="@android:color/black"
                        app:cornerRadius="8dp"/>
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSearchCandidates"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Search Candidates"
                        android:textColor="@android:color/white"
                        app:backgroundTint="@color/primary"
                        app:cornerRadius="8dp"/>
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="?android:colorBackground"
        app:menu="@menu/company_bottom_navigation"
        app:labelVisibilityMode="labeled"
        app:itemIconTint="@color/company_bottom_nav_item_color"
        app:itemTextColor="@color/company_bottom_nav_item_color"
        app:elevation="8dp"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>