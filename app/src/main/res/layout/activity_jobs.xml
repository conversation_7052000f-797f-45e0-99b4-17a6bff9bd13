<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">


        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.DUTCareerHub.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/searchEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Search jobs" />
        </com.google.android.material.textfield.TextInputLayout>


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/fieldFilterContainer"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Filter by field">

            <com.google.android.material.textfield.MaterialAutoCompleteTextView
                android:id="@+id/fieldFilterInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>
        </com.google.android.material.textfield.TextInputLayout>


        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/filterChipGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/filterFullTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Full-time" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/filterPartTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Part-time" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/filterRemote"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Remote" />
            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/jobsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_empty_state"
                android:contentDescription="No jobs found" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="No jobs found"
                android:textSize="18sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Try adjusting your search or filters"
                android:textColor="?android:textColorSecondary" />
        </LinearLayout>

    </LinearLayout>


    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>