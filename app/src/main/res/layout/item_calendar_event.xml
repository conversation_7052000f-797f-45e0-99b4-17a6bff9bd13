<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/eventCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Event Title -->
            <TextView
                android:id="@+id/eventTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                tools:text="Team Meeting" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/editButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_update"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:contentDescription="Edit Event"
                    app:tint="@color/text_secondary" />

                <ImageView
                    android:id="@+id/deleteButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_delete"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:contentDescription="Delete Event"
                    app:tint="@color/error" />

            </LinearLayout>

        </LinearLayout>

        <!-- Time and Duration Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/eventTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                tools:text="2:00 PM" />

            <View
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/text_secondary" />

            <TextView
                android:id="@+id/eventDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                tools:text="1h" />

        </LinearLayout>

        <!-- Meeting Type Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/eventTypeIcon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_schedule"
                app:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/eventType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                tools:text="Online" />

        </LinearLayout>

        <!-- Location/Link Row -->
        <TextView
            android:id="@+id/eventLocation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="https://meet.google.com/abc-defg-hij" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
