<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/eventCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Date Column -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="16dp"
            android:minWidth="60dp">

            <TextView
                android:id="@+id/eventDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                tools:text="Dec 25" />

            <TextView
                android:id="@+id/eventTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginTop="2dp"
                tools:text="2:00 PM" />

        </LinearLayout>

        <!-- Event Details Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Event Title -->
            <TextView
                android:id="@+id/eventTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Team Meeting" />

            <!-- Meeting Type Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/eventTypeIcon"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginEnd="6dp"
                    android:src="@drawable/ic_schedule"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/eventType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    tools:text="Online" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="8dp">

            <ImageView
                android:id="@+id/editButton"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_update"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="2dp"
                android:contentDescription="Edit Event"
                app:tint="@color/text_secondary" />

            <ImageView
                android:id="@+id/deleteButton"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_delete"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="2dp"
                android:contentDescription="Delete Event"
                app:tint="@color/error" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
