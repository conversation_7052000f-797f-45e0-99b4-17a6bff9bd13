<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/jobCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="?attr/colorSurface"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Job Title and Match Score Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/jobTitleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="?android:attr/textColorPrimary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:lineSpacingExtra="2dp"/>

            <LinearLayout
                android:id="@+id/matchContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/bg_match_score"
                android:padding="8dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/matchPercentageText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorPrimary"
                    android:text="85%"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Match"
                    android:textSize="10sp"
                    android:textColor="?android:attr/textColorSecondary"/>

            </LinearLayout>

        </LinearLayout>

        <!-- Company Name -->
        <TextView
            android:id="@+id/companyNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="14sp"
            android:textStyle="italic"
            android:maxLines="1"
            android:ellipsize="end"/>

        <!-- Job Details Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Location -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_location"
                    android:tint="?attr/colorPrimary"
                    android:layout_marginEnd="6dp"/>

                <TextView
                    android:id="@+id/locationText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="13sp"
                    android:maxLines="1"
                    android:ellipsize="end"/>

            </LinearLayout>

            <!-- Salary -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginStart="8dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_money"
                    android:tint="?attr/colorPrimary"
                    android:layout_marginEnd="6dp"/>

                <TextView
                    android:id="@+id/salaryText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="13sp"
                    android:maxLines="1"
                    android:ellipsize="end"/>

            </LinearLayout>

        </LinearLayout>

        <!-- Job Type and Posted Date Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/jobTypeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_job_type_chip"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:textSize="12sp"
                android:textColor="?attr/colorPrimary"
                android:textStyle="bold"/>

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>

            <TextView
                android:id="@+id/postedDateText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?android:attr/textColorTertiary"
                android:textSize="12sp"
                android:text="Posted today"/>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>