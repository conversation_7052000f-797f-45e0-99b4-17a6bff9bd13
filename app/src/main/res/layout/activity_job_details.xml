<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            style="@style/Widget.DUTCareerHub.Toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Job Details">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/saveJobButton"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="end"
                android:layout_marginEnd="8dp"
                android:backgroundTint="@android:color/transparent"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_bookmark_border"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconTint="@android:color/white"
                app:strokeWidth="0dp" />
        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="80dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/jobTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceHeadline5"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        tools:text="Senior Software Engineer" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_business"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/companyName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold"
                            tools:text="Google" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_location"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobLocation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="Mountain View, CA" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_location"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobProvince"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="Western Cape" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_work"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobField"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="Information Technology" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_schedule"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobType"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="Full-time" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_experience"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobExperience"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="3-5 years" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_attach_money"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobSalary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="$100,000 - $150,000 per year" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_calendar"
                            app:tint="?android:attr/textColorPrimary" />

                        <TextView
                            android:id="@+id/jobPostedDate"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary"
                            tools:text="Posted on May 15, 2023" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Job Description"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="@color/primary"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/jobDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:lineSpacingExtra="4dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:textColor="@color/text_primary"
                        tools:text="This is a detailed job description that explains the responsibilities, tasks, and expectations for the role." />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Requirements"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="@color/primary"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/jobRequirements"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:lineSpacingExtra="4dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:textColor="@color/text_primary"
                        tools:text="• Bachelor's degree in Computer Science or related field\n• 3+ years of experience in software development\n• Proficiency in Java, Kotlin, and Android development" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="?android:colorBackground"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/applyButton"
            style="@style/Widget.JobRec.Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/primary"
            android:textColor="@color/white"
            android:text="Apply Now" />

        <LinearLayout
            android:id="@+id/jobManagementLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/editJobButton"
                style="@style/Widget.JobRec.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Edit" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteJobButton"
                style="@style/Widget.JobRec.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="Delete" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>