<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:colorBackground">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary"
            app:elevation="4dp">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                style="@style/Widget.DUTCareerHub.Toolbar"
                app:titleTextAppearance="@style/TextAppearance.MaterialComponents.Headline6" />

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/searchLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Search jobs"
                        app:startIconDrawable="@drawable/ic_search">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/searchInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:imeOptions="actionSearch"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/filterButton"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="Filter"
                        app:icon="@drawable/ic_filter"
                        app:iconGravity="textStart"/>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/resultsCountText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 jobs found"
                android:textStyle="italic"
                android:gravity="end"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone"/>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/searchResultsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"/>

        </LinearLayout>


        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/searchFab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="16dp"
            android:src="@drawable/ic_search"
            app:backgroundTint="@color/black"
            app:tint="@color/white"/>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>


    <include layout="@layout/layout_filter_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>