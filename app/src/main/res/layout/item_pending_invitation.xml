<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_calendar"
                app:tint="@color/primary"
                android:layout_marginEnd="8dp"/>

            <TextView
                android:id="@+id/titleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Interview Invitation"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PENDING"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:background="@drawable/bg_status_pending"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"/>
        </LinearLayout>

        <!-- Company Info -->
        <TextView
            android:id="@+id/companyText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="From: TechCorp"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginBottom="8dp"/>

        <!-- Date and Time -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_schedule"
                app:tint="?android:attr/textColorSecondary"
                android:layout_marginEnd="8dp"/>

            <TextView
                android:id="@+id/dateTimeText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Dec 15, 2024 at 2:00 PM"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"/>

            <TextView
                android:id="@+id/durationText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Duration: 1h 30m"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"/>
        </LinearLayout>

        <!-- Meeting Type -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:id="@+id/meetingTypeIcon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_video_call"
                app:tint="?android:attr/textColorSecondary"
                android:layout_marginEnd="8dp"/>

            <TextView
                android:id="@+id/meetingTypeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Online Meeting"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginEnd="8dp"/>
        </LinearLayout>

        <!-- Location/Link -->
        <TextView
            android:id="@+id/locationText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="https://meet.google.com/abc-defg-hij"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="16dp"
            android:maxLines="2"
            android:ellipsize="end"/>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/declineButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Decline"
                android:textColor="@color/error"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:strokeColor="@color/error"
                app:strokeWidth="1dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/acceptButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Accept"
                android:textColor="@android:color/white"
                app:backgroundTint="@color/success"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
