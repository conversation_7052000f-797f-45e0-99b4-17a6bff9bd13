<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Title Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Event Title"
            app:startIconDrawable="@drawable/ic_schedule">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/titleInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Description Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Description (Optional)">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/descriptionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Date and Time Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <!-- Date Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="Date"
                app:startIconDrawable="@drawable/ic_calendar">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/dateInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Time Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="Time"
                app:startIconDrawable="@drawable/ic_schedule">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/timeInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Duration Spinner -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Duration"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/text_primary" />

        <Spinner
            android:id="@+id/durationSpinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- Meeting Type Spinner -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Meeting Type"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/text_primary" />

        <Spinner
            android:id="@+id/meetingTypeSpinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- Location Input (for in-person meetings) -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Location"
            android:visibility="gone"
            app:startIconDrawable="@drawable/ic_location">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/locationInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Meeting Link Input (for online meetings) -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Meeting Link"
            app:startIconDrawable="@drawable/ic_chatbot">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/meetingLinkInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri"
                android:maxLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Notes Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="Notes (Optional)">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/notesInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="4" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Cancel"
                android:backgroundTint="@color/button_secondary" />

            <Button
                android:id="@+id/saveButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save"
                android:backgroundTint="@color/button_primary" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
